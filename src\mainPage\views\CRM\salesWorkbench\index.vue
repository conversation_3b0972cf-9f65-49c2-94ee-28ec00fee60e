<template>
  <div class="sales-workbench">
    <!-- 顶部导航栏
    <header class="workbench-header">
      <div class="header-container">
        <div class="header-content">
          左侧Logo和标题
          <div class="header-left">
            <div class="logo-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <span class="header-title">销售智能工作台</span>
          </div>

          右侧用户信息和通知
          <div class="header-right">
            <el-badge :value="notificationCount" class="notification-badge">
              <el-button :icon="Bell" circle class="notification-btn" />
            </el-badge>
            <div class="user-info">
              <el-avatar :size="32" :src="userAvatar" />
              <span class="username">{{ userName }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </header>

    快捷操作栏
    <div class="quick-actions">
      <el-button type="primary" :icon="Plus" @click="handleAddCustomer">
        新增客户
      </el-button>
      <el-button :icon="Lightning" @click="handleCreateOpportunity">
        创建商机
      </el-button>
      <div class="urgent-alerts">
        <el-tag type="danger" effect="dark">
          <el-icon><Warning /></el-icon>
          {{ urgentCount }}个紧急
        </el-tag>
      </div>
    </div> -->
    <!-- 主体内容 -->
    <main class="main-content">
      <!-- 页面标题和时间筛选 -->
      <!-- <div class="page-header">
        <div class="page-title-section">
          <h1 class="page-title">业务数据概览</h1>
          <p class="page-subtitle">全面掌握客户、商机、合同、收款数据动态</p>
        </div>
        <div class="time-filter">
          <el-radio-group v-model="timeFilter" class="time-filter-group">
            <el-radio-button label="today">今日</el-radio-button>
            <el-radio-button label="week">本周</el-radio-button>
            <el-radio-button label="month">本月</el-radio-button>
            <el-radio-button label="custom">
              <el-icon><Calendar /></el-icon>自定义
            </el-radio-button>
          </el-radio-group>
        </div>
      </div> -->

      <!-- 关键预警提示 - 置顶显示 -->
      <section class="alert-section">
        <el-alert type="error" :closable="false" class="urgent-alert">
          <template #title>
            <div class="alert-content">
              <el-icon class="alert-icon"><Warning /></el-icon>
              <span
                >您有 <strong>{{ overduePayments }}个逾期收款</strong> 和
                <strong>{{ overdueOpportunities }}个超期未跟进商机</strong> 需要处理</span
              >
              <el-button type="primary" text @click="viewAllAlerts">查看全部提醒</el-button>
            </div>
          </template>
        </el-alert>
      </section>

      <!-- 核心指标卡片 - 最显眼位置 -->
      <section class="metrics-section">
        <h2 class="section-title">
          <el-icon><Odometer /></el-icon>核心业务指标
        </h2>
        <div class="metrics-grid">
          <!-- 客户总数 -->
          <div class="metric-card-new">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">新增客户数</span>
                <span class="metric-summary"
                  >总计
                  {{ metrics.newCustomers.month.value + metrics.newCustomers.year.value }}</span
                >
              </div>
              <div class="metric-icon-new primary">
                <el-icon><User /></el-icon>
              </div>
            </div>
            <div style="display: flex; gap: 20px">
              <div>
                <div class="metric-title">本月</div>
                <div class="metric-value-new">2,841</div>
                <div class="metric-growth-new positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>12.5% 较上月</span>
                </div>
              </div>
              <div>
                <div class="metric-title">本年</div>
                <div class="metric-value-new">2,841</div>
                <div class="metric-growth-new positive">
                  <el-icon><ArrowUp /></el-icon>
                  <span>12.5% 较上月</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 活跃商机 -->
          <div class="metric-card-new warning-border">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">商机数量</span>
                <span class="metric-summary"
                  >总计
                  {{
                    metrics.activeOpportunities.month.value + metrics.activeOpportunities.year.value
                  }}</span
                >
              </div>
              <div class="metric-icon-new warning">
                <el-icon><Lightning /></el-icon>
              </div>
            </div>
            <div style="display: flex; gap: 12px">
              <div style="flex: 1">
                <div class="metric-title">本月</div>
                <div class="metric-value-new">{{ metrics.activeOpportunities.month.value }}</div>
                <div
                  class="metric-growth-new"
                  :class="metrics.activeOpportunities.month.growth >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon
                    ><ArrowUp v-if="metrics.activeOpportunities.month.growth >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ Math.abs(metrics.activeOpportunities.month.growth) }}% 较上月</span>
                </div>
              </div>
              <div style="flex: 1">
                <div class="metric-title">本年</div>
                <div class="metric-value-new">{{ metrics.activeOpportunities.year.value }}</div>
                <div
                  class="metric-growth-new"
                  :class="metrics.activeOpportunities.year.growth >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon
                    ><ArrowUp v-if="metrics.activeOpportunities.year.growth >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ Math.abs(metrics.activeOpportunities.year.growth) }}% 较上年</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 合同金额 -->
          <div class="metric-card-new success-border">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">合同金额</span>
                <span class="metric-summary"
                  >总计 ¥{{
                    metrics.contractAmount.month.value + metrics.contractAmount.year.value
                  }}万</span
                >
              </div>
              <div class="metric-icon-new success">
                <el-icon><Document /></el-icon>
              </div>
            </div>
            <div style="display: flex; gap: 12px">
              <div style="flex: 1">
                <div class="metric-title">本月</div>
                <div class="metric-value-new">¥{{ metrics.contractAmount.month.value }}万</div>
                <div
                  class="metric-growth-new"
                  :class="metrics.contractAmount.month.growth >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon
                    ><ArrowUp v-if="metrics.contractAmount.month.growth >= 0" /><ArrowDown v-else
                  /></el-icon>
                  <span>{{ Math.abs(metrics.contractAmount.month.growth) }}% 较上月</span>
                </div>
              </div>
              <div style="flex: 1">
                <div class="metric-title">本年</div>
                <div class="metric-value-new">¥{{ metrics.contractAmount.year.value }}万</div>
                <div
                  class="metric-growth-new"
                  :class="metrics.contractAmount.year.growth >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon
                    ><ArrowUp v-if="metrics.contractAmount.year.growth >= 0" /><ArrowDown v-else
                  /></el-icon>
                  <span>{{ Math.abs(metrics.contractAmount.year.growth) }}% 较上年</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 已收款金额 -->
          <div class="metric-card-new info-border">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">已收款金额</span>
                <span class="metric-summary"
                  >总计 ¥{{
                    metrics.receivedPayment.month.value + metrics.receivedPayment.year.value
                  }}万</span
                >
              </div>
              <div class="metric-icon-new info">
                <el-icon><Money /></el-icon>
              </div>
            </div>
            <div style="display: flex; gap: 12px">
              <div style="flex: 1">
                <div class="metric-title">本月</div>
                <div class="metric-value-new">¥{{ metrics.receivedPayment.month.value }}万</div>
                <div
                  class="metric-growth-new"
                  :class="metrics.receivedPayment.month.growth >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon
                    ><ArrowUp v-if="metrics.receivedPayment.month.growth >= 0" /><ArrowDown v-else
                  /></el-icon>
                  <span>{{ Math.abs(metrics.receivedPayment.month.growth) }}% 较上月</span>
                </div>
              </div>
              <div style="flex: 1">
                <div class="metric-title">本年</div>
                <div class="metric-value-new">¥{{ metrics.receivedPayment.year.value }}万</div>
                <div
                  class="metric-growth-new"
                  :class="metrics.receivedPayment.year.growth >= 0 ? 'positive' : 'negative'"
                >
                  <el-icon
                    ><ArrowUp v-if="metrics.receivedPayment.year.growth >= 0" /><ArrowDown v-else
                  /></el-icon>
                  <span>{{ Math.abs(metrics.receivedPayment.year.growth) }}% 较上年</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 总应收款 -->
          <div class="metric-card-new danger-border">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">总应收款</span>
                <!-- <span class="metric-summary">当前 ¥{{ metrics.totalReceivable.current }}万</span> -->
              </div>
              <div class="metric-icon-new danger">
                <el-icon><Money /></el-icon>
              </div>
            </div>
            <div class="metric-value-new">¥{{ metrics.totalReceivable.current }}万</div>
            <div class="metric-growth-new warning">
              <el-icon><Warning /></el-icon>
              <span>逾期¥{{ metrics.totalReceivable.overdue }}万</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 业务卡片区域 - 每行三个 -->
      <div class="business-cards-grid">
        <!-- 超期未跟进商机 -->
        <el-card class="business-card overdue-opportunities-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Warning /></el-icon>
              <span>超期未跟进商机</span>
            </div>
          </template>

          <div class="opportunity-list">
            <div
              v-for="opportunity in overdueOpportunitiesList.slice(0, 2)"
              :key="opportunity.id"
              class="opportunity-item"
              :class="{ 'severe-overdue': opportunity.overdueDays > 10 }"
            >
              <div class="opportunity-header">
                <div class="opportunity-info">
                  <p class="opportunity-title">
                    {{ opportunity.customerName }} - {{ opportunity.projectName }}
                  </p>
                  <p class="opportunity-status">
                    {{ opportunity.stage }} · 已超期{{ opportunity.overdueDays }}天
                  </p>
                </div>
                <el-tag :type="opportunity.overdueDays > 10 ? 'danger' : 'warning'" size="small">
                  {{ opportunity.overdueDays > 10 ? '严重超期' : '超期' }}
                </el-tag>
              </div>
              <div class="opportunity-footer">
                <span class="last-follow">上次跟进: {{ opportunity.lastFollowDate }}</span>
                <el-button type="primary" size="small" @click="followUpOpportunity(opportunity)">
                  立即跟进
                </el-button>
              </div>
            </div>
          </div>

          <!-- 商机阶段分布 -->
          <div class="stage-distribution">
            <h3 class="distribution-title">商机阶段分布</h3>
            <div class="stage-grid-compact">
              <div class="stage-item primary">
                <p class="stage-label">新增</p>
                <p class="stage-value">{{ stageDistribution.new }}</p>
              </div>
              <div class="stage-item info">
                <p class="stage-label">方案</p>
                <p class="stage-value">{{ stageDistribution.proposal }}</p>
              </div>
              <div class="stage-item warning">
                <p class="stage-label">报价</p>
                <p class="stage-value">{{ stageDistribution.quotation }}</p>
              </div>
              <div class="stage-item success">
                <p class="stage-label">成交</p>
                <p class="stage-value">{{ stageDistribution.closed }}</p>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 合同续费商机提醒 -->
        <el-card class="business-card renewal-opportunities-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Refresh /></el-icon>
              <span>合同续费商机提醒</span>
            </div>
          </template>

          <div class="renewal-list">
            <div
              v-for="renewal in renewalOpportunities.slice(0, 2)"
              :key="renewal.id"
              class="renewal-item"
              :class="renewal.priority"
            >
              <div class="renewal-header">
                <div class="renewal-info">
                  <p class="renewal-title">
                    {{ renewal.customerName }} - {{ renewal.serviceName }}
                  </p>
                  <p class="renewal-status">合同到期: {{ renewal.daysToExpire }}天后</p>
                </div>
                <el-tag
                  :type="
                    renewal.intentionLevel === 'high'
                      ? 'warning'
                      : renewal.intentionLevel === 'medium'
                      ? 'info'
                      : 'primary'
                  "
                  size="small"
                >
                  {{ renewal.intentionText }}
                </el-tag>
              </div>
              <div class="renewal-footer">
                <span class="current-fee">续费金额: ¥{{ renewal.currentFee }}万</span>
                <el-button type="primary" size="small" @click="createRenewalPlan(renewal)">
                  制定方案
                </el-button>
              </div>
            </div>
          </div>
          <!-- 续费商机价值分析 -->
          <div class="renewal-value-analysis">
            <h3 class="analysis-title">续费商机价值</h3>
            <div class="value-list">
              <div class="value-item">
                <div class="value-header">
                  <span>30天内到期</span>
                  <span>¥{{ renewalValue.within30Days }}万</span>
                </div>
                <el-progress
                  :percentage="35"
                  color="#F56C6C"
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
              <div class="value-item">
                <div class="value-header">
                  <span>31-90天到期</span>
                  <span>¥{{ renewalValue.within90Days }}万</span>
                </div>
                <el-progress
                  :percentage="55"
                  color="#E6A23C"
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
            </div>
          </div>
        </el-card>

        <!-- 报备商机提醒 -->
        <el-card class="business-card report-opportunities-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Bell /></el-icon>
              <span>报备商机提醒</span>
            </div>
          </template>

          <div class="report-list">
            <div
              v-for="report in reportOpportunities.slice(0, 2)"
              :key="report.id"
              class="report-item"
              :class="report.priority"
            >
              <div class="report-header">
                <div class="report-info">
                  <p class="report-title">{{ report.customerName }} - {{ report.projectName }}</p>
                  <p class="report-status">报备到期: {{ report.daysToExpire }}天后</p>
                </div>
                <el-tag
                  :type="
                    report.urgencyLevel === 'urgent'
                      ? 'danger'
                      : report.urgencyLevel === 'warning'
                      ? 'warning'
                      : 'info'
                  "
                  size="small"
                >
                  {{ report.urgencyText }}
                </el-tag>
              </div>
              <div class="report-footer">
                <span class="project-value">项目价值: ¥{{ report.projectValue }}万</span>
                <el-button type="primary" size="small" @click="handleReportReminder(report)">
                  处理报备
                </el-button>
              </div>
            </div>
          </div>
          <!-- 报备商机统计分析 -->
          <div class="report-statistics">
            <h3 class="analysis-title">报备到期统计</h3>
            <div class="statistics-list">
              <div class="statistics-item">
                <div class="statistics-header">
                  <span>15天内到期</span>
                  <span>{{ reportStatistics.within15Days }}个</span>
                </div>
                <el-progress
                  :percentage="65"
                  color="#F56C6C"
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
              <div class="statistics-item">
                <div class="statistics-header">
                  <span>15-30天到期</span>
                  <span>{{ reportStatistics.within30Days }}个</span>
                </div>
                <el-progress
                  :percentage="40"
                  color="#E6A23C"
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
            </div>
          </div>
        </el-card>

        <!-- 客户多维度分析 -->
        <el-card class="business-card customer-analysis-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><UserFilled /></el-icon>
              <span>客户多维度分析</span>
            </div>
          </template>

          <!-- 客户行业分布图表 -->
          <div class="chart-section">
            <h3 class="chart-title">客户行业分布</h3>
            <div class="chart-container-compact" ref="industryChartRef"></div>
          </div>

          <!-- 客户销售额分析 -->
          <div class="customer-ranking-compact">
            <div style="display: flex;gap:20px">
                <div class="ranking-section" style="flex: 1;">
              <h3 class="ranking-title">客户销售额TOP3</h3>
              <div class="ranking-list">
                <div
                  v-for="customer in topSalesCustomers.slice(0, 3)"
                  :key="customer.id"
                  class="ranking-item"
                >
                  <div class="ranking-header">
                    <span class="customer-name">{{ customer.name }}</span>
                    <span class="customer-amount">¥{{ customer.amount }}万</span>
                  </div>
                  <el-progress
                    :percentage="customer.percentage"
                    :show-text="false"
                    :stroke-width="6"
                  />
                </div>
              </div>
            </div>
             <div class="ranking-section" style="flex: 1;">
              <h3 class="ranking-title">客户收款额TOP3</h3>
              <div class="ranking-list">
                <div
                  v-for="customer in topSalesCustomers.slice(0, 3)"
                  :key="customer.id"
                  class="ranking-item"
                >
                  <div class="ranking-header">
                    <span class="customer-name">{{ customer.name }}</span>
                    <span class="customer-amount">¥{{ customer.amount }}万</span>
                  </div>
                  <el-progress
                    :percentage="customer.percentage"
                    :show-text="false"
                    :stroke-width="6"
                      status="success"
                  />
                </div>
              </div>
            </div>
            </div>
          </div>
        </el-card>

        <!-- 合同与收款 - 占两个卡片的宽度 -->
        <el-card class="business-card contract-payment-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Document /></el-icon>
              <span>合同与收款</span>
            </div>
          </template>

          <div class="contract-payment-content">
            <!-- 左侧折线图 - 70% -->
            <div class="chart-area">
              <h3 class="chart-title">合同金额与收款趋势</h3>
              <div class="chart-container-compact" ref="contractPaymentChartRef"></div>
            </div>

            <!-- 右侧合同信息 - 30% -->
            <div class="contract-info-area">
              <!-- 收款预警 -->
              <div class="contract-alerts">
                <h3 class="section-subtitle">收款预警</h3>
                <div class="alert-list">
                  <div
                    v-for="payment in overduePaymentsList.slice(0, 6)"
                    :key="payment.id"
                    class="alert-item overdue-payment"
                  >
                    <div class="alert-info">
                      <p class="contract-number">{{ payment.contractNumber }}</p>
                      <p class="customer-name">{{ payment.customerName }}</p>
                    </div>
                    <div class="alert-amount">
                      <p class="amount">¥{{ payment.amount }}万</p>
                      <p class="overdue-days">逾期{{ payment.overdueDays }}天</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 合同状态分布 -->
              <!-- <div class="contract-status-distribution">
                <h3 class="section-subtitle">合同状态分布</h3>
                <div class="status-grid">
                  <div class="status-item primary">
                    <p class="status-label">执行中</p>
                    <p class="status-value">{{ contractStatus.active }}</p>
                  </div>
                  <div class="status-item warning">
                    <p class="status-label">即将到期</p>
                    <p class="status-value">{{ contractStatus.expiring }}</p>
                  </div>
                  <div class="status-item info">
                    <p class="status-label">待签署</p>
                    <p class="status-value">{{ contractStatus.pending }}</p>
                  </div>
                  <div class="status-item success">
                    <p class="status-label">已完成</p>
                    <p class="status-value">{{ contractStatus.completed }}</p>
                  </div>
                </div>
              </div> -->
            </div>
          </div>
        </el-card>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import {
  Warning,
  UserFilled,
  Refresh,
  Document,
  User,
  ArrowUp,
  ArrowDown,
  Lightning,
  Money,
  Bell,
  Odometer,
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';

// 响应式数据

// 预警数据
const overduePayments = ref(3);
const overdueOpportunities = ref(5);

// 核心指标数据
const metrics = reactive({
  // 新增客户数
  customerStats: {
    week: { value: 28, growth: 15.2 },
    month: { value: 156, growth: 12.5 },
    year: { value: 1842, growth: 18.7 },
  },
  // 活跃商机
  activeOpportunities: {
    week: { value: 45, growth: -8.3 },
    month: { value: 356, growth: -3.2 },
    year: { value: 4128, growth: 5.6 },
  },
  // 合同金额
  contractAmount: {
    week: { value: 18.6, growth: 22.4 },
    month: { value: 86.4, growth: 8.7 },
    year: { value: 1024.8, growth: 15.3 },
  },
  // 已收款金额
  receivedPayment: {
    week: { value: 12.8, growth: 18.9 },
    month: { value: 62.1, growth: 5.3 },
    year: { value: 756.4, growth: 12.1 },
  },
  // 总应收款
  totalReceivable: {
    current: 1268.5, // 截止到现在的总应收款
    overdue: 156.8, // 逾期应收款
    pending: 1111.7, // 待收款
  },
});

// 商机阶段分布
const stageDistribution = reactive({
  new: 124,
  proposal: 86,
  quotation: 78,
  closed: 42,
});

// 超期商机列表
const overdueOpportunitiesList = ref([
  {
    id: 1,
    customerName: '未来科技',
    projectName: '企业服务升级',
    stage: '报价阶段',
    overdueDays: 15,
    lastFollowDate: '2023-06-10',
  },
  {
    id: 2,
    customerName: '云达数据',
    projectName: '系统扩容项目',
    stage: '方案阶段',
    overdueDays: 8,
    lastFollowDate: '2023-06-17',
  },
]);

// 客户排行数据
const topSalesCustomers = ref([
  { id: 1, name: '科技创新有限公司', amount: 248, percentage: 100 },
  { id: 2, name: '未来贸易集团', amount: 186, percentage: 75 },
  { id: 3, name: '智慧云服务公司', amount: 156, percentage: 63 },
  { id: 4, name: '星辰科技', amount: 128, percentage: 52 },
  { id: 5, name: '云达数据', amount: 98, percentage: 39 },
]);

// 续费商机数据
const renewalOpportunities = ref([
  {
    id: 1,
    customerName: '智慧云服务公司',
    serviceName: '年度服务',
    daysToExpire: 30,
    currentFee: 28.5,
    intentionLevel: 'high',
    intentionText: '高意向',
    priority: 'high',
  },
  {
    id: 2,
    customerName: '未来贸易集团',
    serviceName: '系统维护',
    daysToExpire: 60,
    currentFee: 15.2,
    intentionLevel: 'medium',
    intentionText: '中等意向',
    priority: 'medium',
  },
]);

// 报备商机数据
const reportOpportunities = ref([
  {
    id: 1,
    customerName: '科技创新有限公司',
    projectName: '数字化转型项目',
    daysToExpire: 12,
    projectValue: 45.8,
    urgencyLevel: 'urgent',
    urgencyText: '紧急',
    priority: 'high',
  },
  {
    id: 2,
    customerName: '星辰科技',
    projectName: '系统集成服务',
    daysToExpire: 25,
    projectValue: 32.6,
    urgencyLevel: 'warning',
    urgencyText: '提醒',
    priority: 'medium',
  },
]);

// 报备统计数据
const reportStatistics = reactive({
  within15Days: 8, // 15天内到期的报备数量
  within30Days: 15, // 15-30天内到期的报备数量
});

// 续费价值分析
const renewalValue = reactive({
  within30Days: 58.6,
  within90Days: 92.4,
  within180Days: 124.8,
});

// 逾期收款列表
const overduePaymentsList = ref([
  {
    id: 1,
    contractNumber: 'HT-2023-6789',
    customerName: '科技创新有限公司',
    amount: 15.6,
    overdueDays: 3,
  },
  {
    id: 2,
    contractNumber: 'HT-2023-6789',
    customerName: '科技创新有限公司',
    amount: 15.6,
    overdueDays: 3,
  },
  {
    id: 3,
    contractNumber: 'HT-2023-6789',
    customerName: '科技创新有限公司',
    amount: 15.6,
    overdueDays: 3,
  },
  {
    id: 4,
    contractNumber: 'HT-2023-6789',
    customerName: '科技创新有限公司',
    amount: 15.6,
    overdueDays: 3,
  },
  {
    id: 5,
    contractNumber: 'HT-2023-6789',
    customerName: '科技创新有限公司',
    amount: 15.6,
    overdueDays: 3,
  },
]);

// 合同状态分布
const contractStatus = reactive({
  active: 156,
  expiring: 24,
  pending: 18,
  completed: 342,
});

// 图表引用
const industryChartRef = ref(null);
const contractPaymentChartRef = ref(null);


// 方法

const viewAllAlerts = () => {
  console.log('查看全部提醒');
};

const followUpOpportunity = opportunity => {
  console.log('跟进商机:', opportunity);
};

const createRenewalPlan = renewal => {
  console.log('制定续费方案:', renewal);
};

const handleReportReminder = report => {
  console.log('处理报备提醒:', report);
};

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 客户行业分布图表
    if (industryChartRef.value) {
      const industryChart = echarts.init(industryChartRef.value);
      const industryOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} 家 ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            fontSize: 11,
          },
        },
        series: [
          {
            name: '客户行业分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            data: [
              { value: 650, name: '电商' },
              { value: 520, name: '制造' },
              { value: 420, name: '医疗' },
              { value: 380, name: '金融' },
              { value: 310, name: '教育' },
              { value: 561, name: '其他' },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };
      industryChart.setOption(industryOption);
    }

    // 合同与收款趋势图（折线图）
    if (contractPaymentChartRef.value) {
      const contractChart = echarts.init(contractPaymentChartRef.value);
      const contractOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
        },
        legend: {
          data: ['合同金额', '收款金额'],
          top: 10,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月',
          ],
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}万',
          },
        },
        series: [
          {
            name: '合同金额',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
            },
            areaStyle: {
              opacity: 0.1,
            },
            data: [65, 78, 90, 85, 98, 86, 92, 105, 88, 96, 102, 110],
            itemStyle: {
              color: '#165DFF',
            },
          },
          {
            name: '收款金额',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
            },
            areaStyle: {
              opacity: 0.1,
            },
            data: [52, 68, 82, 75, 88, 62, 78, 85, 72, 80, 86, 92],
            itemStyle: {
              color: '#36CFC9',
            },
          },
        ],
      };
      contractChart.setOption(contractOption);
    }

  
  });
};

// 生命周期
onMounted(() => {
  initCharts();
  getDetail()
});

function getDetail() {
  // 获取核心业务指标数据
getCoreBusiness()
}
// 获取核心业务指标数据
function getCoreBusiness() {
  // 获取核心业务指标数据
  axios.get('/api/vt-admin/statistics/workbench_core_index',{
    params: {
      type: 0,
    }
  }).then(res => {
    if (res.data.code === 200) {
    
     metrics.value = res.data.data;
    }

  })
}
</script>

<style scoped>
.sales-workbench {
  min-height: calc(100vh - 120px);
  background-color: #f5f7fa;
  position: relative;
}

/* 顶部导航栏 */
.workbench-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: -10px -6px 0 -6px;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 24px;
  color: #165dff;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-badge {
  position: relative;
}

.notification-btn {
  color: #f56c6c;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.username {
  font-weight: 500;
  color: #1d2129;
}

/* 快捷操作栏 */
.quick-actions {
  position: sticky;
  top: 64px;
  z-index: 99;
  background: white;
  border-bottom: 1px solid #e5e6eb;
  padding: 12px 0;
  margin: 0 -6px 24px -6px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding-left: 16px;
  padding-right: 16px;
}

.urgent-alerts {
  margin-left: auto;
}

/* 主体内容 */
.main-content {
  padding: 0 16px 32px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title-section h1 {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: bold;
  color: #1d2129;
  margin: 0;
}

.page-subtitle {
  color: #4e5969;
  margin-top: 4px;
}

.time-filter-group {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 预警提示 */
.alert-section {
  margin-bottom: 24px;
}

.urgent-alert {
  border-left: 4px solid #f56c6c;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.alert-icon {
  font-size: 20px;
  color: #f56c6c;
}

/* 核心指标卡片 */
.metrics-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

/* 新的指标卡片样式 */
.metric-card-new {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border-left: 4px solid #4a90e2;
  position: relative;
  min-height: 110px;
}

.metric-card-new:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 应用原来的边框颜色 */
.metric-card-new.primary-border {
  border-left-color: #165dff;
}

.metric-card-new.warning-border {
  border-left-color: #faad14;
}

.metric-card-new.success-border {
  border-left-color: #52c41a;
}

.metric-card-new.info-border {
  border-left-color: #40a9ff;
}

.metric-card-new.danger-border {
  border-left-color: #f56c6c;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.metric-title-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric-title {
  font-size: 13px;
  color: #4e5969;
  font-weight: 600;
  margin-bottom: 0;
}

.metric-summary {
  font-size: 11px;
  color: #86909c;
  font-weight: 500;
  background: rgba(134, 144, 156, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  width: fit-content;
}

.metric-icon-new {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.metric-icon-new.primary {
  background: rgba(22, 93, 255, 0.1);
  color: #165dff;
}

.metric-icon-new.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.metric-icon-new.success {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.metric-icon-new.info {
  background: rgba(64, 169, 255, 0.1);
  color: #40a9ff;
}

.metric-icon-new.danger {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.metric-value-new {
  font-size: 24px;
  font-weight: 700;
  color: #1d2129;
  margin-bottom: 6px;
  line-height: 1.2;
}

.metric-growth-new {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.metric-growth-new.positive {
  color: #52c41a;
}

.metric-growth-new.negative {
  color: #f56c6c;
}

.metric-growth-new.warning {
  color: #faad14;
}

.metric-growth-new .el-icon {
  font-size: 12px;
}

/* 保留旧样式以防其他地方使用 */
.metric-card {
  background: white;
  border-radius: 12px;
  padding: 18px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  min-height: 130px;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.primary-border {
  border-left-color: #165dff;
}

.warning-border {
  border-left-color: #faad14;
}

.success-border {
  border-left-color: #52c41a;
}

.info-border {
  border-left-color: #40a9ff;
}

.danger-border {
  border-left-color: #f56c6c;
}

.metric-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
}

.metric-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.metric-label {
  color: #4e5969;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  color: #1d2129;
  margin: 8px 0;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  margin: 0;
}

.metric-trend.success {
  color: #52c41a;
}

.metric-trend.danger {
  color: #f56c6c;
}

.trend-label {
  color: #4e5969;
}

.overdue-tag {
  margin-left: 8px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
}

.metric-icon.primary {
  background: rgba(22, 93, 255, 0.1);
  color: #165dff;
}

.metric-icon.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.metric-icon.success {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.metric-icon.info {
  background: rgba(64, 169, 255, 0.1);
  color: #40a9ff;
}

.metric-icon.danger {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

/* 时间维度数据样式 */
.metric-periods {
  margin-top: 0;
  display: flex;
  gap: 12px;
  justify-content: space-between;
}

/* 两列布局样式 */
.metric-periods-two {
  gap: 16px;
}

.metric-periods-two .period-item {
  flex: 1;
  padding: 10px 8px;
}

.period-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 0;
  padding: 8px 6px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #e5e6eb;
}

.period-label {
  font-size: 11px;
  color: #86909c;
  margin-bottom: 4px;
  font-weight: 500;
}

.period-value {
  font-size: 14px;
  font-weight: 700;
  color: #1d2129;
  margin-bottom: 3px;
  line-height: 1;
}

.period-growth {
  font-size: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 2px;
}

.period-growth.positive {
  color: #52c41a;
}

.period-growth.negative {
  color: #f56c6c;
}

.period-growth .el-icon {
  font-size: 10px;
}

/* 单值显示样式 */
.metric-single-value {
  margin-top: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.single-value-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
}

.single-value-label {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 6px;
  font-weight: 500;
}

.single-value-amount {
  font-size: 20px;
  font-weight: 700;
  color: #1d2129;
  margin-bottom: 8px;
  line-height: 1;
}

.receivable-breakdown {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.breakdown-item {
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  padding: 3px 6px;
  border-radius: 4px;
}

.breakdown-item.overdue {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}

.breakdown-item.pending {
  color: #faad14;
  background: rgba(250, 173, 20, 0.1);
}

/* 业务卡片网格布局 */
.business-cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 32px;
}

/* 合同与收款卡片占两个卡片宽度 */
.contract-payment-card {
  grid-column: span 2;
}

@media (max-width: 1200px) {
  .business-cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contract-payment-card {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .business-cards-grid {
    grid-template-columns: 1fr;
  }

  .contract-payment-card {
    grid-column: span 1;
  }
}

.contract-payment-content {
  display: flex;
  gap: 16px;
  height: 100%;
}

/* 左侧图表区域 - 70% */
.chart-area {
  flex: 0 0 65%;
  display: flex;
  flex-direction: column;
}

/* 右侧合同信息区域 - 30% */
.contract-info-area {
  flex: 0 0 35%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-left: 16px;
  box-sizing: border-box;
  border-left: 1px solid #e5e6eb;
}

.section-subtitle {
  font-size: 13px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.section-subtitle::before {
  content: '';
  width: 3px;
  height: 12px;
  background: #165dff;
  border-radius: 2px;
}

/* 合同预警样式 */
.contract-alerts {
  flex-shrink: 0;
}

.contract-alerts .alert-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 0;
}

.contract-alerts .alert-item {
  padding: 8px;
  border-radius: 6px;
  border: 1px solid rgba(245, 108, 108, 0.2);
  background: rgba(245, 108, 108, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.contract-number {
  font-weight: 500;
  font-size: 12px;
  color: #1d2129;
  margin: 0;
}

.customer-name {
  font-size: 11px;
  color: #4e5969;
  margin: 0;
}

.alert-amount {
  text-align: right;
}

.amount {
  font-weight: 500;
  font-size: 12px;
  color: #f56c6c;
  margin: 0;
}

.overdue-days {
  font-size: 10px;
  color: #f56c6c;
  margin: 0;
}

/* 合同状态分布 */
.contract-status-distribution {
  flex: 1;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.status-item {
  padding: 8px;
  border-radius: 6px;
  border: 1px solid;
  text-align: center;
}

.status-item.primary {
  background: rgba(22, 93, 255, 0.05);
  border-color: rgba(22, 93, 255, 0.2);
}

.status-item.warning {
  background: rgba(250, 173, 20, 0.05);
  border-color: rgba(250, 173, 20, 0.2);
}

.status-item.info {
  background: rgba(64, 169, 255, 0.05);
  border-color: rgba(64, 169, 255, 0.2);
}

.status-item.success {
  background: rgba(82, 196, 26, 0.05);
  border-color: rgba(82, 196, 26, 0.2);
}

.status-label {
  font-size: 11px;
  color: #4e5969;
  margin: 0 0 3px 0;
}

.status-value {
  font-size: 16px;
  font-weight: bold;
  margin: 0;
}

.status-item.primary .status-value {
  color: #165dff;
}

.status-item.warning .status-value {
  color: #faad14;
}

.status-item.info .status-value {
  color: #40a9ff;
}

.status-item.success .status-value {
  color: #52c41a;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .contract-payment-content {
    flex-direction: column;
    gap: 12px;
  }

  .chart-area {
    flex: none;
  }

  .contract-info-area {
    flex: none;
    padding-left: 0;
    border-left: none;
    border-top: 1px solid #e5e6eb;
    padding-top: 12px;
  }

  .status-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .contract-payment-content {
    gap: 8px;
  }

  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 业务卡片样式 */
.business-card {
  height: auto;
  min-height: 380px;
  max-height: 450px;
  overflow: hidden;
}

.business-card .el-card__body {
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 卡片通用样式 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1d2129;
}

.header-icon {
  color: #165dff;
}

/* 超期商机卡片 */
.opportunity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.opportunity-item {
  padding: 10px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  background: #fafbfc;
  transition: all 0.3s ease;
}

.opportunity-item.severe-overdue {
  border-color: rgba(245, 108, 108, 0.3);
  background: rgba(245, 108, 108, 0.05);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}

.opportunity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.opportunity-title {
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  margin: 0;
}

.opportunity-status {
  font-size: 12px;
  color: #f56c6c;
  margin: 4px 0 0 0;
}

.opportunity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-follow {
  font-size: 12px;
  color: #4e5969;
}

/* 商机阶段分布 */
.stage-distribution {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e6eb;
  flex-shrink: 0;
}

.distribution-title {
  font-size: 14px;
  font-weight: 500;
  color: #4e5969;
  margin-bottom: 12px;
}

.stage-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stage-grid-compact {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 6px;
}

.stage-grid-compact .stage-item {
  padding: 8px;
}

.stage-grid-compact .stage-label {
  font-size: 11px;
}

.stage-grid-compact .stage-value {
  font-size: 16px;
  margin-top: 2px;
}

.stage-item {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid;
}

.stage-item.primary {
  background: rgba(22, 93, 255, 0.05);
  border-color: rgba(22, 93, 255, 0.2);
}

.stage-item.info {
  background: rgba(64, 169, 255, 0.05);
  border-color: rgba(64, 169, 255, 0.2);
}

.stage-item.warning {
  background: rgba(250, 173, 20, 0.05);
  border-color: rgba(250, 173, 20, 0.2);
}

.stage-item.success {
  background: rgba(82, 196, 26, 0.05);
  border-color: rgba(82, 196, 26, 0.2);
}

.stage-label {
  font-size: 12px;
  color: #4e5969;
  margin: 0;
}

.stage-value {
  font-size: 20px;
  font-weight: bold;
  margin: 4px 0 0 0;
}

.stage-item.primary .stage-value {
  color: #165dff;
}

.stage-item.info .stage-value {
  color: #40a9ff;
}

.stage-item.warning .stage-value {
  color: #faad14;
}

.stage-item.success .stage-value {
  color: #52c41a;
}

/* 客户分析卡片 */
.chart-section {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.chart-title {
  font-size: 13px;
  font-weight: 500;
  color: #4e5969;
  margin-bottom: 8px;
}

.chart-container {
  height: 260px;
  width: 100%;
}

.chart-container-compact {
  height: 150px;
  width: 100%;
}

.chart-container.large {
  height: 320px;
}

/* 客户排行 */
.customer-ranking {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.customer-ranking-compact {
  margin-top: 12px;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .customer-ranking {
    grid-template-columns: 1fr;
  }
}

.ranking-title {
  font-size: 13px;
  font-weight: 500;
  color: #4e5969;
  margin-bottom: 8px;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ranking-item {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.customer-name {
  font-weight: 500;
  color: #1d2129;
}

.customer-amount {
  color: #4e5969;
}

/* 续费商机卡片 */
.renewal-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.renewal-item {
  padding: 10px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.renewal-item.high {
  border-color: rgba(250, 173, 20, 0.3);
  background: rgba(250, 173, 20, 0.05);
}

.renewal-item.medium {
  border-color: rgba(64, 169, 255, 0.3);
  background: rgba(64, 169, 255, 0.05);
}

.renewal-item.low {
  border-color: rgba(22, 93, 255, 0.3);
  background: rgba(22, 93, 255, 0.05);
}

.renewal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.renewal-title {
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  margin: 0;
}

.renewal-status {
  font-size: 12px;
  margin: 4px 0 0 0;
}

.renewal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-fee {
  font-size: 12px;
  color: #4e5969;
}

/* 续费价值分析 */
.renewal-value-analysis {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e6eb;
  flex-shrink: 0;
}

.analysis-title {
  font-size: 14px;
  font-weight: 500;
  color: #4e5969;
  margin-bottom: 12px;
}

.value-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.value-item {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.value-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #4e5969;
}

/* 报备商机卡片样式 */
.report-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.report-item {
  padding: 10px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.report-item.high {
  border-color: rgba(245, 108, 108, 0.3);
  background: rgba(245, 108, 108, 0.05);
}

.report-item.medium {
  border-color: rgba(250, 173, 20, 0.3);
  background: rgba(250, 173, 20, 0.05);
}

.report-item.low {
  border-color: rgba(64, 169, 255, 0.3);
  background: rgba(64, 169, 255, 0.05);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.report-title {
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  margin: 0;
}

.report-status {
  font-size: 12px;
  margin: 4px 0 0 0;
  color: #f56c6c;
}

.report-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-value {
  font-size: 12px;
  color: #4e5969;
}

/* 报备统计分析 */
.report-statistics {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e6eb;
  flex-shrink: 0;
}

.statistics-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.statistics-item {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #4e5969;
}

/* 分析区域 */
.analysis-section {
  margin-bottom: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-title {
    display: none;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .customer-ranking {
    grid-template-columns: 1fr;
  }

  .stage-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 88px 8px 32px;
  }

  .quick-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .urgent-alerts {
    margin-left: 0;
  }
}
</style>
